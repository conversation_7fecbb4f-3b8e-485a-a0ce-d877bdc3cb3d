# Environment Variables Reference

This document provides a comprehensive reference for all environment variables supported by OpenWebUI in the MerlinHelm deployment.

## 🔧 Microsoft OAuth Configuration

OpenWebUI supports Microsoft OAuth authentication through the following environment variables:

### MICROSOFT_CLIENT_ID
- **Type**: `str`
- **Description**: Sets the client ID for Microsoft OAuth
- **Persistence**: PersistentConfig variable
- **Example**: `"your-client-id-12345"`
- **Environment-specific values**:
  - **Local**: `"local-client-id-12345"`
  - **Dev**: `"dev-client-id-12345"`
  - **Test**: `"test-client-id-12345"`
  - **Prod**: `"prod-client-id-12345"`

### MICROSOFT_CLIENT_SECRET
- **Type**: `str`
- **Description**: Sets the client secret for Microsoft OAuth
- **Persistence**: PersistentConfig variable
- **Security**: Stored in Kubernetes secrets
- **Example**: `"your-client-secret-67890"`
- **Environment-specific values**:
  - **Local**: `"local-client-secret-67890"`
  - **Dev**: `"dev-client-secret-67890"`
  - **Test**: `"test-client-secret-67890"`
  - **Prod**: `"prod-client-secret-67890"`

### MICROSOFT_CLIENT_TENANT_ID
- **Type**: `str`
- **Description**: Sets the tenant ID for Microsoft OAuth
- **Persistence**: PersistentConfig variable
- **Example**: `"your-tenant-id-abcde"`
- **Environment-specific values**:
  - **Local**: `"local-tenant-id-abcde"`
  - **Dev**: `"dev-tenant-id-abcde"`
  - **Test**: `"test-tenant-id-abcde"`
  - **Prod**: `"prod-tenant-id-abcde"`

### MICROSOFT_OAUTH_SCOPE
- **Type**: `str`
- **Default**: `"openid email profile"`
- **Description**: Sets the scope for Microsoft OAuth authentication
- **Persistence**: PersistentConfig variable
- **Example**: `"openid email profile"`

### MICROSOFT_REDIRECT_URI
- **Type**: `str`
- **Default**: `"<backend>/oauth/microsoft/callback"`
- **Description**: Sets the redirect URI for Microsoft OAuth
- **Persistence**: PersistentConfig variable
- **Environment-specific values**:
  - **Local**: `"http://openwebui.local.merlinhelm.local:31080/oauth/microsoft/callback"`
  - **Dev**: `"https://openwebui.dev.merlinhelm.local/oauth/microsoft/callback"`
  - **Test**: `"https://openwebui.test.merlinhelm.local/oauth/microsoft/callback"`
  - **Prod**: `"https://openwebui.merlinhelm.com/oauth/microsoft/callback"`

## 🤖 OpenAI API Configuration

OpenWebUI supports OpenAI API integration through the following environment variables:

### ENABLE_OPENAI_API
- **Type**: `bool`
- **Default**: `true`
- **Description**: Enables the use of OpenAI APIs
- **Persistence**: PersistentConfig variable
- **Example**: `"true"`

### OPENAI_API_BASE_URL
- **Type**: `str`
- **Default**: `"https://api.openai.com/v1"`
- **Description**: Configures the OpenAI base API URL
- **Persistence**: PersistentConfig variable
- **Example**: `"https://api.openai.com/v1"`

### OPENAI_API_KEY
- **Type**: `str`
- **Description**: Sets the OpenAI API key
- **Persistence**: PersistentConfig variable
- **Security**: Stored in Kubernetes secrets
- **Example**: `"sk-124781258123"`
- **Environment-specific values**:
  - **Local**: `"sk-local-api-key-123456789"`
  - **Dev**: `"sk-dev-api-key-123456789"`
  - **Test**: `"sk-test-api-key-123456789"`
  - **Prod**: `"sk-prod-api-key-123456789"`

## 🔐 Security Configuration

### Secret Management

Sensitive environment variables are stored in Kubernetes secrets:

- `MICROSOFT_CLIENT_SECRET`: Stored in secret key `microsoft-client-secret`
- `OPENAI_API_KEY`: Stored in secret key `openai-api-key`
- `WEBUI_SECRET_KEY`: Stored in secret key `secret-key` (auto-generated)

### Secret Configuration in Helm Values

```yaml
secret:
  create: true
  secretKey: ""  # Auto-generated if empty
  microsoftClientSecret: "your-client-secret"
  openaiApiKey: "your-api-key"
```

## 🌍 Environment-Specific Configuration

### Microsoft OAuth Enablement

- **Local**: Disabled (`enabled: false`)
- **Dev**: Disabled (`enabled: false`)
- **Test**: Disabled (`enabled: false`)
- **Prod**: Enabled (`enabled: true`)

### OpenAI API Enablement

- **Local**: Enabled (`enabled: true`)
- **Dev**: Enabled (`enabled: true`)
- **Test**: Enabled (`enabled: true`)
- **Prod**: Enabled (`enabled: true`)

## 📝 Configuration Examples

### Helm Values Configuration

```yaml
config:
  # Microsoft OAuth Configuration
  microsoftOAuth:
    enabled: true
    clientId: "your-client-id"
    clientSecret: "your-client-secret"
    tenantId: "your-tenant-id"
    scope: "openid email profile"
    redirectUri: "https://your-domain.com/oauth/microsoft/callback"

  # OpenAI API Configuration
  openaiApi:
    enabled: true
    baseUrl: "https://api.openai.com/v1"
    apiKey: "your-api-key"

# Secret configuration
secret:
  microsoftClientSecret: "your-client-secret"
  openaiApiKey: "your-api-key"
```

### Environment Variables in Deployment

The following environment variables are automatically configured in the OpenWebUI deployment:

```yaml
env:
  # Microsoft OAuth
  - name: MICROSOFT_CLIENT_ID
    value: "your-client-id"
  - name: MICROSOFT_CLIENT_SECRET
    valueFrom:
      secretKeyRef:
        name: openwebui-secret
        key: microsoft-client-secret
  - name: MICROSOFT_CLIENT_TENANT_ID
    value: "your-tenant-id"
  - name: MICROSOFT_OAUTH_SCOPE
    value: "openid email profile"
  - name: MICROSOFT_REDIRECT_URI
    value: "https://your-domain.com/oauth/microsoft/callback"

  # OpenAI API
  - name: ENABLE_OPENAI_API
    value: "true"
  - name: OPENAI_API_BASE_URL
    value: "https://api.openai.com/v1"
  - name: OPENAI_API_KEY
    valueFrom:
      secretKeyRef:
        name: openwebui-secret
        key: openai-api-key
```

## 🔄 Updating Configuration

To update environment variables, follow these steps:

### Step 1: Update Helm Values

Edit the appropriate environment file with your new values:

```yaml
# Example: environments/prod/values.yaml
openwebui:
  config:
    microsoftOAuth:
      clientId: "your-new-client-id"
      tenantId: "your-new-tenant-id"
    openaiApi:
      baseUrl: "https://your-custom-api.com/v1"

  secret:
    microsoftClientSecret: "your-new-client-secret"
    openaiApiKey: "your-new-api-key"
```

### Step 2: Rebuild Dependencies (Important!)

When you modify the OpenWebUI chart configuration, you **must** rebuild the merlin-stack dependencies:

#### PowerShell (Windows)
```powershell
# Rebuild chart dependencies
helm dependency update helm/merlin-stack

# Verify dependencies are updated
helm dependency list helm/merlin-stack
```

#### Bash (Linux/macOS)
```bash
# Rebuild chart dependencies
helm dependency update helm/merlin-stack

# Verify dependencies are updated
helm dependency list helm/merlin-stack
```

### Step 3: Redeploy

Choose the appropriate deployment method based on your changes:

#### Option A: Upgrade Deployment (Recommended)
Use this for configuration changes that don't require pod recreation:

```powershell
# PowerShell
.\scripts\deploy.ps1 prod -Upgrade

# Bash
./scripts/deploy.sh prod --upgrade
```

#### Option B: Force Redeploy
Use this when upgrade doesn't pick up changes or for major configuration changes:

```powershell
# PowerShell
.\scripts\deploy.ps1 prod -Force

# Bash
./scripts/deploy.sh prod --force
```

### Step 4: Verify Configuration

After deployment, verify the environment variables are properly set:

```powershell
# Check deployment configuration
kubectl describe deployment merlinhelm-prod-openwebui -n merlinhelm-prod

# Verify environment variables in running pod
kubectl exec -n merlinhelm-prod deployment/merlinhelm-prod-openwebui -- env | Select-String "MICROSOFT|OPENAI"

# Check pod logs for any configuration issues
kubectl logs -n merlinhelm-prod deployment/merlinhelm-prod-openwebui
```

### Complete Update Example

Here's a complete example of updating OpenAI API configuration:

```powershell
# 1. Edit the values file
# Edit environments/prod/values.yaml and update openaiApi.baseUrl

# 2. Rebuild dependencies
helm dependency update helm/merlin-stack

# 3. Redeploy with force to ensure changes take effect
.\scripts\deploy.ps1 prod -Force

# 4. Verify the changes
kubectl exec -n merlinhelm-prod deployment/merlinhelm-prod-openwebui -- env | Select-String "OPENAI_API_BASE_URL"
```

## 🛠 Troubleshooting

### Common Issues

1. **Environment variables not appearing in pod**:
   - **Cause**: Chart dependencies not rebuilt after changes
   - **Solution**: Run `helm dependency update helm/merlin-stack` then redeploy with `-Force`

2. **Changes not taking effect after upgrade**:
   - **Cause**: Kubernetes may not restart pods for certain configuration changes
   - **Solution**: Use force redeploy instead of upgrade: `.\scripts\deploy.ps1 env -Force`

3. **Secret not found**:
   - **Cause**: `secret.create: false` or missing secret configuration
   - **Solution**: Ensure `secret.create: true` in values.yaml

4. **OAuth not working**:
   - **Cause**: Incorrect redirect URI or domain mismatch
   - **Solution**: Verify redirect URI matches your actual domain and port

5. **API key invalid**:
   - **Cause**: Incorrect API key in secret or wrong format
   - **Solution**: Check the secret contains valid API key with correct prefix (sk- for OpenAI)

6. **Deployment shows old environment variables**:
   - **Cause**: Using cached chart dependencies
   - **Solution**: Delete and rebuild dependencies:
     ```powershell
     Remove-Item helm/merlin-stack/charts/*.tgz
     helm dependency update helm/merlin-stack
     .\scripts\deploy.ps1 env -Force
     ```

### Verification Commands

```powershell
# Check if dependencies are up to date
helm dependency list helm/merlin-stack

# Check secrets exist
kubectl get secrets -n merlinhelm-prod

# Check environment variables in pod (Windows)
kubectl exec -n merlinhelm-prod deployment/merlinhelm-prod-openwebui -- env | Select-String "MICROSOFT|OPENAI"

# Check environment variables in pod (Linux/macOS)
kubectl exec -n merlinhelm-prod deployment/merlinhelm-prod-openwebui -- env | grep -E "(MICROSOFT|OPENAI)"

# Check deployment configuration
kubectl describe deployment merlinhelm-prod-openwebui -n merlinhelm-prod

# Check pod logs for configuration issues
kubectl logs -n merlinhelm-prod deployment/merlinhelm-prod-openwebui

# Check if pods are using latest configuration
kubectl get pods -n merlinhelm-prod -o wide
```

### Debug Steps for Missing Environment Variables

If environment variables are not appearing in your pods, follow these debug steps:

```powershell
# 1. Check if chart dependencies are current
helm dependency list helm/merlin-stack

# 2. Force rebuild dependencies
helm dependency update helm/merlin-stack

# 3. Verify the values are in the chart
helm template test helm/merlin-stack --values environments/local/values.yaml | Select-String "MICROSOFT|OPENAI"

# 4. Force redeploy to ensure fresh pods
.\scripts\deploy.ps1 local -Force

# 5. Wait for pods to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=openwebui -n merlinhelm-local --timeout=120s

# 6. Verify environment variables in running pod
kubectl exec -n merlinhelm-local deployment/merlinhelm-local-openwebui -- env | Select-String "MICROSOFT|OPENAI"
```

## 📚 References

- [OpenWebUI Documentation](https://docs.openwebui.com/)
- [Microsoft OAuth Documentation](https://docs.microsoft.com/en-us/azure/active-directory/develop/)
- [OpenAI API Documentation](https://platform.openai.com/docs/)

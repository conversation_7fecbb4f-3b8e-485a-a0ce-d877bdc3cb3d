# Environment Variables Reference

This document provides a comprehensive reference for all environment variables supported by OpenWebUI in the MerlinHelm deployment.

## 🔧 Microsoft OAuth Configuration

OpenWebUI supports Microsoft OAuth authentication through the following environment variables:

### MICROSOFT_CLIENT_ID
- **Type**: `str`
- **Description**: Sets the client ID for Microsoft OAuth
- **Persistence**: PersistentConfig variable
- **Example**: `"your-client-id-12345"`
- **Environment-specific values**:
  - **Local**: `"local-client-id-12345"`
  - **Dev**: `"dev-client-id-12345"`
  - **Test**: `"test-client-id-12345"`
  - **Prod**: `"prod-client-id-12345"`

### MICROSOFT_CLIENT_SECRET
- **Type**: `str`
- **Description**: Sets the client secret for Microsoft OAuth
- **Persistence**: PersistentConfig variable
- **Security**: Stored in Kubernetes secrets
- **Example**: `"your-client-secret-67890"`
- **Environment-specific values**:
  - **Local**: `"local-client-secret-67890"`
  - **Dev**: `"dev-client-secret-67890"`
  - **Test**: `"test-client-secret-67890"`
  - **Prod**: `"prod-client-secret-67890"`

### MICROSOFT_CLIENT_TENANT_ID
- **Type**: `str`
- **Description**: Sets the tenant ID for Microsoft OAuth
- **Persistence**: PersistentConfig variable
- **Example**: `"your-tenant-id-abcde"`
- **Environment-specific values**:
  - **Local**: `"local-tenant-id-abcde"`
  - **Dev**: `"dev-tenant-id-abcde"`
  - **Test**: `"test-tenant-id-abcde"`
  - **Prod**: `"prod-tenant-id-abcde"`

### MICROSOFT_OAUTH_SCOPE
- **Type**: `str`
- **Default**: `"openid email profile"`
- **Description**: Sets the scope for Microsoft OAuth authentication
- **Persistence**: PersistentConfig variable
- **Example**: `"openid email profile"`

### MICROSOFT_REDIRECT_URI
- **Type**: `str`
- **Default**: `"<backend>/oauth/microsoft/callback"`
- **Description**: Sets the redirect URI for Microsoft OAuth
- **Persistence**: PersistentConfig variable
- **Environment-specific values**:
  - **Local**: `"http://openwebui.local.merlinhelm.local:31080/oauth/microsoft/callback"`
  - **Dev**: `"https://openwebui.dev.merlinhelm.local/oauth/microsoft/callback"`
  - **Test**: `"https://openwebui.test.merlinhelm.local/oauth/microsoft/callback"`
  - **Prod**: `"https://openwebui.merlinhelm.com/oauth/microsoft/callback"`

## 🤖 OpenAI API Configuration

OpenWebUI supports OpenAI API integration through the following environment variables:

### ENABLE_OPENAI_API
- **Type**: `bool`
- **Default**: `true`
- **Description**: Enables the use of OpenAI APIs
- **Persistence**: PersistentConfig variable
- **Example**: `"true"`

### OPENAI_API_BASE_URL
- **Type**: `str`
- **Default**: `"https://api.openai.com/v1"`
- **Description**: Configures the OpenAI base API URL
- **Persistence**: PersistentConfig variable
- **Example**: `"https://api.openai.com/v1"`

### OPENAI_API_KEY
- **Type**: `str`
- **Description**: Sets the OpenAI API key
- **Persistence**: PersistentConfig variable
- **Security**: Stored in Kubernetes secrets
- **Example**: `"sk-124781258123"`
- **Environment-specific values**:
  - **Local**: `"sk-local-api-key-123456789"`
  - **Dev**: `"sk-dev-api-key-123456789"`
  - **Test**: `"sk-test-api-key-123456789"`
  - **Prod**: `"sk-prod-api-key-123456789"`

## 🔐 Security Configuration

### Secret Management

Sensitive environment variables are stored in Kubernetes secrets:

- `MICROSOFT_CLIENT_SECRET`: Stored in secret key `microsoft-client-secret`
- `OPENAI_API_KEY`: Stored in secret key `openai-api-key`
- `WEBUI_SECRET_KEY`: Stored in secret key `secret-key` (auto-generated)

### Secret Configuration in Helm Values

```yaml
secret:
  create: true
  secretKey: ""  # Auto-generated if empty
  microsoftClientSecret: "your-client-secret"
  openaiApiKey: "your-api-key"
```

## 🌍 Environment-Specific Configuration

### Microsoft OAuth Enablement

- **Local**: Disabled (`enabled: false`)
- **Dev**: Disabled (`enabled: false`)
- **Test**: Disabled (`enabled: false`)
- **Prod**: Enabled (`enabled: true`)

### OpenAI API Enablement

- **Local**: Enabled (`enabled: true`)
- **Dev**: Enabled (`enabled: true`)
- **Test**: Enabled (`enabled: true`)
- **Prod**: Enabled (`enabled: true`)

## 📝 Configuration Examples

### Helm Values Configuration

```yaml
config:
  # Microsoft OAuth Configuration
  microsoftOAuth:
    enabled: true
    clientId: "your-client-id"
    clientSecret: "your-client-secret"
    tenantId: "your-tenant-id"
    scope: "openid email profile"
    redirectUri: "https://your-domain.com/oauth/microsoft/callback"

  # OpenAI API Configuration
  openaiApi:
    enabled: true
    baseUrl: "https://api.openai.com/v1"
    apiKey: "your-api-key"

# Secret configuration
secret:
  microsoftClientSecret: "your-client-secret"
  openaiApiKey: "your-api-key"
```

### Environment Variables in Deployment

The following environment variables are automatically configured in the OpenWebUI deployment:

```yaml
env:
  # Microsoft OAuth
  - name: MICROSOFT_CLIENT_ID
    value: "your-client-id"
  - name: MICROSOFT_CLIENT_SECRET
    valueFrom:
      secretKeyRef:
        name: openwebui-secret
        key: microsoft-client-secret
  - name: MICROSOFT_CLIENT_TENANT_ID
    value: "your-tenant-id"
  - name: MICROSOFT_OAUTH_SCOPE
    value: "openid email profile"
  - name: MICROSOFT_REDIRECT_URI
    value: "https://your-domain.com/oauth/microsoft/callback"
  
  # OpenAI API
  - name: ENABLE_OPENAI_API
    value: "true"
  - name: OPENAI_API_BASE_URL
    value: "https://api.openai.com/v1"
  - name: OPENAI_API_KEY
    valueFrom:
      secretKeyRef:
        name: openwebui-secret
        key: openai-api-key
```

## 🔄 Updating Configuration

To update environment variables:

1. **Update Helm values** in the appropriate environment file
2. **Redeploy** using the deployment scripts
3. **Verify** the configuration in the running pods

### Example Update Process

```bash
# Update values in environments/prod/values.yaml
# Then redeploy
./scripts/deploy.sh prod --upgrade
```

## 🛠 Troubleshooting

### Common Issues

1. **Secret not found**: Ensure `secret.create: true` in values.yaml
2. **OAuth not working**: Verify redirect URI matches your domain
3. **API key invalid**: Check the secret contains valid API key

### Verification Commands

```bash
# Check secrets
kubectl get secrets -n merlinhelm-prod

# Check environment variables in pod
kubectl exec -n merlinhelm-prod deployment/openwebui -- env | grep -E "(MICROSOFT|OPENAI)"

# Check pod logs
kubectl logs -n merlinhelm-prod deployment/openwebui
```

## 📚 References

- [OpenWebUI Documentation](https://docs.openwebui.com/)
- [Microsoft OAuth Documentation](https://docs.microsoft.com/en-us/azure/active-directory/develop/)
- [OpenAI API Documentation](https://platform.openai.com/docs/)

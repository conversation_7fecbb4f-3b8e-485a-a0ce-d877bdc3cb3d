# Default values for merlin-stack
# This is a YAML-formatted file.

# Global configuration
global:
  environment: dev
  domain: merlinhelm.local
  storageClass: ""

# Ollama configuration
ollama:
  enabled: true
  replicaCount: 1

  image:
    repository: ollama/ollama
    tag: "latest"
    pullPolicy: IfNotPresent

  resources:
    limits:
      nvidia.com/gpu: 1
      memory: 8Gi
      cpu: 2000m
    requests:
      memory: 4Gi
      cpu: 1000m

  persistence:
    enabled: true
    size: 50Gi
    storageClass: ""

  nodeSelector:
    accelerator: nvidia-tesla-k80

  tolerations:
    - key: nvidia.com/gpu
      operator: Exists
      effect: NoSchedule

  service:
    type: ClusterIP
    port: 11434

  models:
    preload: []

# OpenWebUI configuration
openwebui:
  enabled: true
  replicaCount: 1

  image:
    repository: ghcr.io/open-webui/open-webui
    tag: "main"
    pullPolicy: IfNotPresent

  resources:
    limits:
      memory: 2Gi
      cpu: 1000m
    requests:
      memory: 512Mi
      cpu: 250m

  persistence:
    enabled: true
    size: 10Gi
    storageClass: ""

  service:
    type: ClusterIP
    port: 8080

  ingress:
    enabled: true
    className: "nginx"
    annotations:
      nginx.ingress.kubernetes.io/rewrite-target: /
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
    hosts:
      - host: openwebui.merlinhelm.local
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: openwebui-tls
        hosts:
          - openwebui.merlinhelm.local

  config:
    ollamaBaseUrl: "http://ollama:11434"
    enableSignup: true
    defaultUserRole: "pending"
    webUIName: "MerlinHelm WebUI"

    # Microsoft OAuth Configuration
    microsoftOAuth:
      enabled: false
      clientId: "dummy-client-id-12345"
      clientSecret: "dummy-client-secret-67890"
      tenantId: "dummy-tenant-id-abcde"
      scope: "openid email profile"
      redirectUri: "<backend>/oauth/microsoft/callback"

    # OpenAI API Configuration
    openaiApi:
      enabled: true
      baseUrl: "https://api.openai.com/v1"
      apiKey: "sk-dummy-api-key-123456789"

  # Secret configuration
  secret:
    microsoftClientSecret: "dummy-client-secret-67890"
    openaiApiKey: "sk-dummy-api-key-123456789"

# Monitoring (optional)
monitoring:
  enabled: false
  prometheus:
    enabled: false
  grafana:
    enabled: false

# Network policies
networkPolicies:
  enabled: false

# Pod disruption budgets
podDisruptionBudget:
  enabled: false
  minAvailable: 1

# Deployment Best Practices

This guide covers best practices for deploying and updating MerlinHelm configurations, especially when making changes to charts and environment variables.

## 🔄 Understanding the Deployment Architecture

MerlinHelm uses a **parent-child chart structure**:

- **merlin-stack** (parent chart) - Contains the overall deployment configuration
- **openwebui** (child chart) - Individual OpenWebUI chart
- **ollama** (child chart) - Individual Ollama chart

### Key Concept: Chart Dependencies

The merlin-stack chart packages the child charts as dependencies. When you modify a child chart (like adding environment variables to OpenWebUI), you **must** rebuild the dependencies for changes to take effect.

## 📋 When to Rebuild Dependencies

### Always Rebuild When:
- ✅ Adding or modifying environment variables
- ✅ Changing chart templates in `helm/openwebui/` or `helm/ollama/`
- ✅ Updating chart configurations or values structure
- ✅ Modifying secrets or configmaps
- ✅ Adding new Kubernetes resources

### No Rebuild Needed When:
- ❌ Only changing values in environment-specific files (`environments/*/values.yaml`)
- ❌ Updating resource limits or replica counts
- ❌ Changing ingress hostnames or domains
- ❌ Modifying existing environment variable values (not structure)

## 🛠 Deployment Workflow

### Standard Update Process

```powershell
# 1. Make your changes
# Edit helm/openwebui/values.yaml or templates

# 2. Rebuild dependencies (CRITICAL!)
helm dependency update helm/merlin-stack

# 3. Verify dependencies
helm dependency list helm/merlin-stack

# 4. Test with dry-run
helm template test helm/merlin-stack --values environments/local/values.yaml --dry-run

# 5. Deploy with force (recommended for structural changes)
.\scripts\deploy.ps1 local -Force

# 6. Verify deployment
kubectl exec -n merlinhelm-local deployment/merlinhelm-local-openwebui -- env | Select-String "YOUR_CHANGE"
```

### Quick Value Updates

For simple value changes without structural modifications:

```powershell
# 1. Edit environment values
# Edit environments/local/values.yaml

# 2. Rebuild dependencies (still required!)
helm dependency update helm/merlin-stack

# 3. Upgrade deployment
.\scripts\deploy.ps1 local -Upgrade

# 4. If upgrade doesn't work, use force
.\scripts\deploy.ps1 local -Force
```

## 🔍 Verification and Testing

### Pre-Deployment Verification

```powershell
# Check chart syntax
helm lint helm/merlin-stack

# Verify dependencies are current
helm dependency list helm/merlin-stack

# Test template rendering
helm template test helm/merlin-stack --values environments/local/values.yaml --dry-run

# Check for specific changes
helm template test helm/merlin-stack --values environments/local/values.yaml | Select-String "MICROSOFT|OPENAI"
```

### Post-Deployment Verification

```powershell
# Check deployment status
kubectl get deployments -n merlinhelm-local

# Verify pods are running
kubectl get pods -n merlinhelm-local

# Check environment variables
kubectl exec -n merlinhelm-local deployment/merlinhelm-local-openwebui -- env | Select-String "MICROSOFT|OPENAI"

# Verify secrets
kubectl get secrets -n merlinhelm-local

# Check logs for issues
kubectl logs -n merlinhelm-local deployment/merlinhelm-local-openwebui
```

## 🚨 Common Pitfalls and Solutions

### Problem: Environment Variables Not Appearing

**Symptoms:**
- New environment variables don't show up in pods
- `kubectl exec` doesn't show expected variables

**Causes & Solutions:**

1. **Forgot to rebuild dependencies**
   ```powershell
   # Solution:
   helm dependency update helm/merlin-stack
   .\scripts\deploy.ps1 local -Force
   ```

2. **Used upgrade instead of force for structural changes**
   ```powershell
   # Solution:
   .\scripts\deploy.ps1 local -Force
   ```

3. **Cached chart dependencies**
   ```powershell
   # Solution:
   Remove-Item helm/merlin-stack/charts/*.tgz
   helm dependency update helm/merlin-stack
   .\scripts\deploy.ps1 local -Force
   ```

### Problem: Changes Don't Take Effect After Upgrade

**Symptoms:**
- Deployment shows old configuration
- Environment variables have old values

**Solution:**
```powershell
# Always use force for environment variable changes
.\scripts\deploy.ps1 local -Force
```

### Problem: Deployment Fails After Changes

**Symptoms:**
- Helm deployment fails
- Pods fail to start

**Debug Steps:**
```powershell
# 1. Check Helm template syntax
helm template test helm/merlin-stack --values environments/local/values.yaml --dry-run

# 2. Check for YAML syntax errors
helm lint helm/merlin-stack

# 3. Check pod events
kubectl describe pod <pod-name> -n merlinhelm-local

# 4. Check deployment events
kubectl describe deployment merlinhelm-local-openwebui -n merlinhelm-local
```

## 📝 Environment-Specific Deployment

### Local Environment
```powershell
# For development and testing
helm dependency update helm/merlin-stack
.\scripts\deploy.ps1 local -Force
```

### Development Environment
```powershell
# For team development
helm dependency update helm/merlin-stack
.\scripts\deploy.ps1 dev -Force
```

### Production Environment
```powershell
# For production (use upgrade when possible)
helm dependency update helm/merlin-stack
.\scripts\deploy.ps1 prod -Upgrade

# Use force only when necessary
.\scripts\deploy.ps1 prod -Force
```

## 🔐 Security Considerations

### Secret Management
- Always use Kubernetes secrets for sensitive data
- Never commit real credentials to version control
- Use different secrets for each environment

### Deployment Safety
```powershell
# Always test in local/dev first
.\scripts\deploy.ps1 local -Force
# Verify everything works
# Then deploy to higher environments
.\scripts\deploy.ps1 prod -Upgrade
```

## 📊 Monitoring Deployments

### Health Checks
```powershell
# Check overall health
kubectl get all -n merlinhelm-local

# Monitor pod status
kubectl get pods -n merlinhelm-local -w

# Check resource usage
kubectl top pods -n merlinhelm-local
```

### Rollback if Needed
```powershell
# List releases
helm list -n merlinhelm-local

# Rollback to previous version
helm rollback merlinhelm-local -n merlinhelm-local

# Or redeploy previous configuration
git checkout HEAD~1 -- environments/local/values.yaml
helm dependency update helm/merlin-stack
.\scripts\deploy.ps1 local -Force
```

## 🎯 Quick Reference

### Essential Commands
```powershell
# Rebuild dependencies (always required for chart changes)
helm dependency update helm/merlin-stack

# Force redeploy (recommended for environment variables)
.\scripts\deploy.ps1 local -Force

# Verify environment variables
kubectl exec -n merlinhelm-local deployment/merlinhelm-local-openwebui -- env | Select-String "MICROSOFT|OPENAI"

# Check deployment status
kubectl describe deployment merlinhelm-local-openwebui -n merlinhelm-local
```

### Troubleshooting Checklist
- [ ] Rebuilt dependencies with `helm dependency update`
- [ ] Used `-Force` flag for structural changes
- [ ] Verified template rendering with `helm template`
- [ ] Checked pod logs for errors
- [ ] Verified environment variables in running pod

## 📚 Related Documentation

- [Environment Variables Reference](environment-variables.md)
- [Getting Started Guide](../GETTING_STARTED.md)
- [Troubleshooting Guide](troubleshooting.md)
- [Parameter Reference](../PARAMETER_REFERENCE.md)

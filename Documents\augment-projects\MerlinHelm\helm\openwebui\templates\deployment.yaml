apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "openwebui.fullname" . }}
  labels:
    {{- include "openwebui.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "openwebui.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "openwebui.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "openwebui.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.targetPort }}
              protocol: TCP
          {{- if .Values.livenessProbe }}
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          {{- end }}
          {{- if .Values.readinessProbe }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            - name: OLLAMA_BASE_URL
              value: {{ .Values.config.ollamaBaseUrl | quote }}
            - name: WEBUI_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "openwebui.fullname" . }}-secret
                  key: secret-key
            - name: ENABLE_SIGNUP
              value: "true"
            - name: DEFAULT_USER_ROLE
              value: "pending"
            # Microsoft OAuth environment variables
            - name: MICROSOFT_CLIENT_ID
              value: {{ .Values.config.microsoftOAuth.clientId | quote }}
            - name: MICROSOFT_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "openwebui.fullname" . }}-secret
                  key: microsoft-client-secret
            - name: MICROSOFT_CLIENT_TENANT_ID
              value: {{ .Values.config.microsoftOAuth.tenantId | quote }}
            - name: MICROSOFT_OAUTH_SCOPE
              value: {{ .Values.config.microsoftOAuth.scope | quote }}
            - name: MICROSOFT_REDIRECT_URI
              value: {{ .Values.config.microsoftOAuth.redirectUri | quote }}
            # OpenAI API environment variables
            - name: ENABLE_OPENAI_API
              value: {{ .Values.config.openaiApi.enabled | quote }}
            - name: OPENAI_API_BASE_URL
              value: {{ .Values.config.openaiApi.baseUrl | quote }}
            - name: OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "openwebui.fullname" . }}-secret
                  key: openai-api-key
            {{- if .Values.env }}
            {{- range .Values.env }}
            {{- if and (ne .name "WEBUI_SECRET_KEY") (ne .name "OLLAMA_BASE_URL") (ne .name "ENABLE_SIGNUP") (ne .name "DEFAULT_USER_ROLE") (ne .name "MICROSOFT_CLIENT_ID") (ne .name "MICROSOFT_CLIENT_SECRET") (ne .name "MICROSOFT_CLIENT_TENANT_ID") (ne .name "MICROSOFT_OAUTH_SCOPE") (ne .name "MICROSOFT_REDIRECT_URI") (ne .name "ENABLE_OPENAI_API") (ne .name "OPENAI_API_BASE_URL") (ne .name "OPENAI_API_KEY") }}
            - {{- toYaml . | nindent 14 }}
            {{- end }}
            {{- end }}
            {{- end }}
          {{- if .Values.persistence.enabled }}
          volumeMounts:
            - name: openwebui-data
              mountPath: {{ .Values.persistence.mountPath }}
          {{- end }}
      {{- if .Values.persistence.enabled }}
      volumes:
        - name: openwebui-data
          persistentVolumeClaim:
            claimName: {{ include "openwebui.fullname" . }}-data
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}

{{- if .Values.secret.create }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "openwebui.fullname" . }}-secret
  labels:
    {{- include "openwebui.labels" . | nindent 4 }}
type: Opaque
data:
  secret-key: {{ include "openwebui.secretKey" . | b64enc | quote }}
  microsoft-client-secret: {{ .Values.secret.microsoftClientSecret | b64enc | quote }}
  openai-api-key: {{ .Values.secret.openaiApiKey | b64enc | quote }}
{{- end }}
